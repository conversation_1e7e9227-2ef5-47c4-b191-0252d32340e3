/**
 * 真正的业务流程 E2E 测试
 * 测试完整的用户业务流程，而不是底层数据操作
 */

const request = require('supertest');
const { createTestApp, cleanupTestApp } = require('../helpers/testApp');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');

describe('Business Flow E2E Tests', () => {
  let app;
  let server;

  beforeAll(async () => {
    await connectTestDB();
    const testAppResult = await createTestApp();
    app = testAppResult.app;
    server = testAppResult.server;
  });

  afterAll(async () => {
    await cleanupTestApp();
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('GraphQL Business Operations', () => {
    test('should query customer by phone via GraphQL', async () => {
      // 测试根据手机号查询客户（实际存在的查询）
      const customerQuery = `
        query CustomerByPhone($phone: String!, $restaurantBrandId: ID!) {
          customerbyPhone(phone: $phone, restaurantBrandId: $restaurantBrandId) {
            customerId
            name
            phone
            email
            isActive
            addressCount
            totalOrders
            totalAmount
          }
        }
      `;

      const variables = {
        phone: '+**********',
        restaurantBrandId: '507f1f77bcf86cd799439011'
      };

      const response = await request(app)
        .post('/graphql')
        .send({
          query: customerQuery,
          variables: variables
        })
        .expect(200);

      // 这个查询应该成功，即使没有找到客户也应该返回 null
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerbyPhone).toBeNull(); // 没有数据时应该返回 null

      console.log('✅ Customer query by phone executed successfully');
    });

    test('should query nearby restaurants via GraphQL', async () => {
      // 测试附近餐厅查询（实际存在的查询）
      const nearbyQuery = `
        query NearByRestaurants($latitude: Float, $longitude: Float, $shopType: String) {
          nearByRestaurants(latitude: $latitude, longitude: $longitude, shopType: $shopType) {
            restaurants {
              _id
              name
              image
              address
              isActive
            }
            sections {
              _id
              name
            }
          }
        }
      `;

      const variables = {
        latitude: 40.730610,
        longitude: -73.935242,
        shopType: "RESTAURANT"
      };

      const response = await request(app)
        .post('/graphql')
        .send({
          query: nearbyQuery,
          variables: variables
        })
        .expect(200);

      expect(response.body.data).toBeDefined();

      if (response.body.errors) {
        console.log('Nearby restaurants query errors (expected in test env):', response.body.errors[0].message);
        expect(response.body.errors).toBeDefined();
        // 当有错误时，data 可能为 null 或包含 null 字段
        if (response.body.data) {
          expect(response.body.data.nearByRestaurants).toBeNull();
        }
      } else {
        expect(response.body.data.nearByRestaurants).toBeDefined();
        expect(response.body.data.nearByRestaurants.restaurants).toBeDefined();
        expect(Array.isArray(response.body.data.nearByRestaurants.restaurants)).toBe(true);
        console.log('Found restaurants:', response.body.data.nearByRestaurants.restaurants.length);
      }

      console.log('✅ Nearby restaurants query executed successfully');
    });

    test('should test order placement mutation schema', async () => {
      // 测试订单下单 mutation（使用正确的参数结构）
      const placeOrderMutation = `
        mutation PlaceOrder(
          $restaurantId: ID!
          $customerId: String!
          $orderInput: [OrderInput!]!
          $paymentMethod: String!
          $orderDate: String!
          $isPickedUp: Boolean!
          $taxationAmount: Float!
          $deliveryCharges: Float!
        ) {
          placeOrder(
            restaurantId: $restaurantId
            customerId: $customerId
            orderInput: $orderInput
            paymentMethod: $paymentMethod
            orderDate: $orderDate
            isPickedUp: $isPickedUp
            taxationAmount: $taxationAmount
            deliveryCharges: $deliveryCharges
          ) {
            _id
            orderId
            orderStatus
            orderAmount
            customerId
          }
        }
      `;

      const variables = {
        restaurantId: '507f1f77bcf86cd799439011',
        customerId: 'customer_123',
        orderInput: [
          {
            food: 'food_1',
            variation: 'var_1',
            quantity: 2,
            specialInstructions: 'No onions'
          }
        ],
        paymentMethod: 'COD',
        orderDate: new Date().toISOString(),
        isPickedUp: false,
        taxationAmount: 2.08,
        deliveryCharges: 3.00
      };

      const response = await request(app)
        .post('/graphql')
        .send({
          query: placeOrderMutation,
          variables: variables
        })
        .expect(200);

      // 这个测试主要验证 mutation 的 schema 是否正确
      if (response.body.errors) {
        console.log('Order placement validation (expected):', response.body.errors[0].message);
        // 在没有真实数据的情况下，这些错误是预期的
        expect(response.body.errors).toBeDefined();
      } else {
        expect(response.body.data.placeOrder).toBeDefined();
        console.log('✅ Order placement mutation schema is valid');
      }
    });
  });

  describe('API Endpoint Integration', () => {
    test('should handle WhatsApp webhook simulation', async () => {
      // 模拟 WhatsApp webhook 调用
      const webhookPayload = {
        object: 'whatsapp_business_account',
        entry: [{
          id: 'test_entry_id',
          changes: [{
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: '+**********',
                phone_number_id: 'test_phone_id'
              },
              messages: [{
                from: '+**********',
                id: 'test_message_id',
                timestamp: '**********',
                text: {
                  body: 'Hello, I want to order food'
                },
                type: 'text'
              }]
            },
            field: 'messages'
          }]
        }]
      };

      const response = await request(app)
        .post('/whatsapp/webhook')
        .send(webhookPayload);

      // WhatsApp webhook 可能返回 200, 401, 404, 或 405，都是可以接受的
      expect([200, 401, 404, 405]).toContain(response.status);
      console.log('WhatsApp webhook response status:', response.status);
    });

    test('should validate application configuration', async () => {
      // 验证应用配置是否正确
      const healthResponse = await request(app)
        .get('/health')
        .expect(200);

      expect(healthResponse.body.status).toBe('ok');
      expect(healthResponse.body.timestamp).toBeDefined();
      // environment 字段可能不存在，这是可以接受的

      console.log('Application health check passed');
      console.log('Health response:', healthResponse.body);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle malformed GraphQL queries gracefully', async () => {
      const malformedQuery = `
        query {
          invalidField {
            nonExistentProperty
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ query: malformedQuery })
        .expect(400);

      expect(response.body.errors).toBeDefined();
      expect(response.body.errors.length).toBeGreaterThan(0);
      console.log('Handled malformed query error correctly');
    });

    test('should handle missing authentication gracefully', async () => {
      const protectedQuery = `
        query {
          profile {
            _id
            email
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ query: protectedQuery })
        .expect(200);

      // 应该返回 null 或错误，而不是崩溃
      if (response.body.data && response.body.data.profile === null) {
        console.log('Correctly handled unauthenticated request');
        expect(response.body.data.profile).toBeNull();
      } else if (response.body.errors) {
        console.log('Authentication error handled correctly');
        expect(response.body.errors).toBeDefined();
      }
    });

    test('should handle large payload gracefully', async () => {
      // 测试大负载处理
      const largeQuery = `
        query {
          __schema {
            types {
              name
              description
              fields {
                name
                description
                type {
                  name
                  description
                }
              }
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ query: largeQuery })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.__schema).toBeDefined();
      console.log('Large payload handled successfully');
    });
  });
});
